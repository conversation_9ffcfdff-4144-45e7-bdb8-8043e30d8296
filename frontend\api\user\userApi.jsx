import { privateAPIClient } from '../index';

export const fetchUserProfile = async () => {
    const response = await privateAPIClient.get(
        '/user-service/api/v1/user/profile'
    );
    return response.data.user;
};

export const updateUserProfile = async (data) => {
    if (data.imageData) {
        const formData = new FormData();

        Object.keys(data).forEach((key) => {
            if (
                key !== 'imageData' &&
                data[key] !== null &&
                data[key] !== undefined
            ) {
                formData.append(key, data[key]);
            }
        });

        if (data.imageData) {
            formData.append('avatar', {
                uri: data.imageData.uri,
                type: data.imageData.type,
                name: data.imageData.filename,
            });
        }

        const response = await privateAPIClient.put(
            '/user-service/api/v1/user/profile',
            formData,
            {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
                transformRequest: (formData) => formData,
            }
        );
        return response.data;
    } else {
        const response = await privateAPIClient.put(
            '/user-service/api/v1/user/profile',
            data
        );
        return response.data;
    }
};
